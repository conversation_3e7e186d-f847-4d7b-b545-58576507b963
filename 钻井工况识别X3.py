# 北京大学智能学院
# ZEROLab实验室
# 学号：2303562246
# 学生：孜克如拉·艾尼瓦尔

import pandas as pd
import numpy as np
import lightgbm as lgb
import time
import os
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    confusion_matrix,
    classification_report
)
from imblearn.over_sampling import SMOTE
from imblearn.under_sampling import RandomUnderSampler
from imblearn.pipeline import Pipeline
import matplotlib.font_manager as fm
import warnings
import shutil
import random

# 设置所有随机种子以确保结果可重现
random.seed(42)
np.random.seed(42)


# 设置中文字体
def set_chinese_font():
    """设置matplotlib的中文字体"""
    try:
        # 尝试不同的中文字体路径
        possible_font_paths = [
            'C:/Windows/Fonts/SimHei.ttf',  # Windows系统
            'C:/Windows/Fonts/msyh.ttf',  # 微软雅黑
            'C:/Windows/Fonts/simsun.ttc',  # 宋体
            '/System/Library/Fonts/PingFang.ttc',  # macOS系统
            '/usr/share/fonts/truetype/droid/DroidSansFallback.ttf',  # Linux系统
        ]

        font_path = None
        for path in possible_font_paths:
            if os.path.exists(path):
                font_path = path
                break

        if font_path:
            prop = fm.FontProperties(fname=font_path)
            plt.rcParams['font.sans-serif'] = [prop.get_name()]
            plt.rcParams['axes.unicode_minus'] = False
            return True
        else:
            # 备用方案：尝试使用系统字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'sans-serif']
            plt.rcParams['axes.unicode_minus'] = False
            return True
    except Exception as e:
        warnings.warn(f"设置中文字体失败: {e}")
        return False


# 设置中文字体
set_chinese_font()


class DrillingConditionIdentifier:
    """基于LightGBM的钻井工况智能识别系统"""

    def __init__(self, output_dir="drilling_results"):
        """初始化模型和参数"""
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.training_history = []
        self.original_to_encoded_map = {}
        self.encoded_to_original_map = {}
        self.workstate_mapping = {
            0: "倒划眼",
            1: "划眼",
            2: "循环钻井液",
            3: "接立柱",
            4: "空井",
            5: "连续下钻具",
            6: "连续提钻具",
            7: "钻进"
        }

        # 创建输出文件夹
        self.output_dir = output_dir
        if os.path.exists(self.output_dir):
            # 如果文件夹已存在，备份旧文件夹
            backup_dir = f"{self.output_dir}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.move(self.output_dir, backup_dir)
            print(f"旧文件夹已备份到: {backup_dir}")

        os.makedirs(self.output_dir, exist_ok=True)
        print(f"创建输出文件夹: {self.output_dir}")

        # 记录训练过程
        self.log_entries = []

    def log(self, message):
        """记录处理日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)
        self.log_entries.append(log_entry)

    def generate_intelligent_labels(self, df):
        """使用改进的基于领域知识的智能标签生成"""
        self.log("使用改进的领域知识生成高质量标签...")

        # 创建工况标签列
        df["工况"] = -1

        # 确保所需列存在
        required_columns = ["井深", "钻头位置", "转盘转速", "总泵冲", "大钩负荷", "钻压"]
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            self.log(f"缺少生成标签所需的列: {missing_columns}，将使用统计方法")
            # 使用更合理的统计分布
            num_samples = len(df)
            # 根据钻井作业的典型分布调整权重
            class_weights = [0.02, 0.02, 0.08, 0.05, 0.3, 0.08, 0.08, 0.37]
            labels = np.random.choice(
                np.arange(8),
                size=num_samples,
                p=class_weights,
                replace=True
            )
            df["工况"] = labels
            self.log("已生成基于统计分布的工况标签")
            return df

        # 基于领域知识的精确规则生成标签
        self.log("使用精确的领域知识规则生成工况标签...")

        # 计算变化量和移动窗口统计
        window_size = 5

        # 核心参数计算
        df['井深变化'] = df['井深'].diff().abs()
        df['钻头位置变化'] = df['钻头位置'].diff().abs()
        df['转盘转速平均'] = df['转盘转速'].rolling(window=window_size, min_periods=1).mean()
        df['总泵冲平均'] = df['总泵冲'].rolling(window=window_size, min_periods=1).mean()
        df['大钩负荷平均'] = df['大钩负荷'].rolling(window=window_size, min_periods=1).mean()
        df['钻压平均'] = df['钻压'].rolling(window=window_size, min_periods=1).mean()

        # 复合条件判断
        #钻进工况：井深增加，钻头位置下降，转盘转速高，钻压高
        mask_drilling = (
                (df['井深变化'] > 0.3) &  # 井深增加
                (df['钻头位置'].diff() < -0.5) &  # 钻头位置下降
                (df['转盘转速平均'] > 20) &  # 转盘转速较高
                (df['钻压平均'] > 10) & # 钻压较高
                ( df['总泵冲平均'] > 5) &
                ( df['钻压平均'] > 3)
        )
        df.loc[mask_drilling, '工况'] = 7

        #空井工况：井深不变，钻头位置不变，转盘转速低
        mask_empty = (
                (df['井深变化'] < 0.01) &  # 井深不变
                (df['钻头位置变化'] < 0.1) &  # 钻头位置不变
                (df['转盘转速平均'] < 5) &  # 转盘转速低
                (df['总泵冲平均'] < 10)  # 泵冲低
        )
        df.loc[mask_empty & (df['工况'] == -1), '工况'] = 4

        #循环钻井液：转盘转速低，泵冲高，井深不变
        mask_circulation = (
                (df['转盘转速平均'] < 10) &  # 转盘转速低
                (df['总泵冲平均'] > 40) &  # 泵冲高
                (df['井深变化'] < 0.01)  # 井深不变
        )
        df.loc[mask_circulation & (df['工况'] == -1), '工况'] = 2

        # 连续下钻具：井深不变，钻头位置持续下降，转盘转速适中
        mask_run_in = (
                (df['井深变化'] < 0.01) &  # 井深不变
                (df['钻头位置'].diff() < -1) &  # 钻头位置快速下降
                (df['转盘转速平均'] > 10) &  # 转盘转速适中
                (df['转盘转速平均'] < 50)
        )
        df.loc[mask_run_in & (df['工况'] == -1), '工况'] = 5

        # 连续提钻具：井深不变，钻头位置持续上升
        mask_run_out = (
                (df['井深变化'] < 0.01) &  # 井深不变
                (df['钻头位置'].diff() > 1) &  # 钻头位置快速上升
                (df['大钩负荷平均'] > 50)  # 大钩负荷适中
        )
        df.loc[mask_run_out & (df['工况'] == -1), '工况'] = 6

        # 划眼/倒划眼：转盘转速高，井深变化小
        mask_reaming = (
                (df['转盘转速平均'] > 30) &  # 转盘转速高
                (df['井深变化'] < 0.5) &  # 井深变化小
                (df['钻压平均'] > 5)  # 钻压不为零
        )
        df.loc[mask_reaming & (df['钻头位置'].diff() > 0.5) & (df['工况'] == -1), '工况'] = 0  # 倒划眼
        df.loc[mask_reaming & (df['钻头位置'].diff() < -0.5) & (df['工况'] == -1), '工况'] = 1  # 划眼

        # 接立柱：其他情况
        df.loc[(df['工况'] == -1), '工况'] = 3

        # 调整分布使其更接近实际
        self._adjust_label_distribution(df)

        # 记录分布
        label_counts = df['工况'].value_counts().sort_index()
        self.log("基于领域知识生成的工况标签分布:")
        for label, count in label_counts.items():
            self.log(
                f"  工况 {int(label)} - {self.workstate_mapping.get(int(label), '未知')}: {count}条 ({count / len(df) * 100:.1f}%)")

        return df

    def _adjust_label_distribution(self, df):
        """调整标签分布使其更接近实际钻井作业分布"""
        current_dist = df['工况'].value_counts()
        total_samples = len(df)

        # 目标分布
        target_dist = {
            0: int(total_samples * 0.02),  # 倒划眼
            1: int(total_samples * 0.02),  # 划眼
            2: int(total_samples * 0.08),  # 循环钻井液
            3: int(total_samples * 0.05),  # 接立柱
            4: int(total_samples * 0.3),  # 空井
            5: int(total_samples * 0.08),  # 连续下钻具
            6: int(total_samples * 0.08),  # 连续提钻具
            7: int(total_samples * 0.37),  # 钻进
        }

        # 调整分布
        for label, target_count in target_dist.items():
            current_count = current_dist.get(label, 0)
            if current_count < target_count:
                # 需要增加样本
                diff = target_count - current_count
                indices = df[df['工况'] == 3].sample(n=min(diff, len(df[df['工况'] == 3])),
                                                     random_state=42).index
                df.loc[indices, '工况'] = label

    def find_label_column(self, df):
        """查找可能的标签列"""
        self.log("查找可能的标签列...")

        potential_label_columns = [
            "工况", "condition", "label", "state", "class", "工况类型",
            "钻井工况", "drilling_condition", "workstate"
        ]

        for col in potential_label_columns:
            if col in df.columns:
                self.log(f"找到可能的标签列: {col}")
                return col

        self.log("未找到可能的标签列")
        return None

    def load_data(self, file_path="optimized_data.csv"):
        """加载预处理后的数据"""
        self.log(f"加载预处理数据: {file_path}")

        try:
            df = pd.read_csv(file_path, encoding="utf-8")
            self.log(f"数据加载成功: {df.shape[0]}行, {df.shape[1]}列")

            # 尝试查找标签列
            label_column = self.find_label_column(df)

            # 如果找到标签列，重命名为"工况"
            if label_column and label_column != "工况":
                df.rename(columns={label_column: "工况"}, inplace=True)
                self.log(f"已将列 '{label_column}' 重命名为 '工况'")

            # 如果未找到标签列，使用改进的标签生成
            if "工况" not in df.columns:
                df = self.generate_intelligent_labels(df)

            # 确保工况列是整数类型
            df["工况"] = df["工况"].astype(int)

            return df
        except Exception as e:
            self.log(f"错误: 数据加载失败 - {str(e)}")
            return None

    def encode_labels(self, df):
        """对标签进行编码"""
        self.log("对工况标签进行编码...")

        # 获取唯一的标签值
        unique_labels = df["工况"].unique()
        self.log(f"原始唯一标签: {sorted(unique_labels)}")

        # 使用标签编码器重新编码标签
        encoded_labels = self.label_encoder.fit_transform(df["工况"])

        # 创建映射关系
        self.original_to_encoded_map = {int(orig): int(enc) for orig, enc in
                                        zip(self.label_encoder.classes_, range(len(self.label_encoder.classes_)))}
        self.encoded_to_original_map = {int(enc): int(orig) for orig, enc in self.original_to_encoded_map.items()}

        # 使用编码后的标签替换原始标签
        df["工况"] = encoded_labels

        # 创建工况名称的映射
        self.encoded_workstate_mapping = {}
        for encoded, original in self.encoded_to_original_map.items():
            self.encoded_workstate_mapping[int(encoded)] = self.workstate_mapping.get(int(original),
                                                                                      f"未知工况({original})")

        return df

    def apply_advanced_feature_engineering(self, df):
        """应用高级特征工程"""
        self.log("应用高级特征工程...")

        try:
            # 时间序列特征 - 移动窗口统计
            window_sizes = [5, 10, 15]
            for window in window_sizes:
                # 移动平均
                df[f'井深_ma{window}'] = df['井深'].rolling(window=window, min_periods=1).mean()
                df[f'钻头位置_ma{window}'] = df['钻头位置'].rolling(window=window, min_periods=1).mean()
                df[f'转盘转速_ma{window}'] = df['转盘转速'].rolling(window=window, min_periods=1).mean()
                df[f'钻压_ma{window}'] = df['钻压'].rolling(window=window, min_periods=1).mean()

                # 移动标准差
                df[f'转盘转速_std{window}'] = df['转盘转速'].rolling(window=window, min_periods=1).std().fillna(0)
                df[f'钻压_std{window}'] = df['钻压'].rolling(window=window, min_periods=1).std().fillna(0)

            # 一阶差分
            fields_to_diff = ['井深', '钻头位置', '转盘转速', '钻压', '大钩负荷', '总泵冲']
            for field in fields_to_diff:
                if field in df.columns:
                    df[f'{field}_diff'] = df[field].diff().fillna(0)
                    df[f'{field}_diff_abs'] = df[f'{field}_diff'].abs()

            # 3. 比率特征
            if '钻压' in df.columns and '大钩负荷' in df.columns:
                df['钻压大钩负荷比'] = df['钻压'] / (df['大钩负荷'] + 1e-5)

            if '转盘转速' in df.columns and '总泵冲' in df.columns:
                df['转速泵冲比'] = df['转盘转速'] / (df['总泵冲'] + 1e-5)

            # 滞后特征
            lag_fields = ['转盘转速', '钻压', '井深']
            for field in lag_fields:
                if field in df.columns:
                    df[f'{field}_lag1'] = df[field].shift(1).fillna(0)
                    df[f'{field}_lag2'] = df[field].shift(2).fillna(0)

            # 加速度特征（二阶差分）
            df['井深_acceleration'] = df['井深_diff'].diff().fillna(0)
            df['钻头位置_acceleration'] = df['钻头位置_diff'].diff().fillna(0)

            # 交互特征
            df['转速_x_钻压'] = df['转盘转速'] * df['钻压']
            df['井深变化_x_转速'] = df['井深_diff'] * df['转盘转速']

            # 周期性特征
            if '创建时间' in df.columns:
                try:
                    df['hour'] = pd.to_datetime(df['创建时间']).dt.hour
                    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
                    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
                except:
                    pass

            # 工况状态稳定性指标
            window = 10
            for field in ['转盘转速', '钻压', '总泵冲']:
                if field in df.columns:
                    df[f'{field}_volatility'] = df[field].rolling(window=window, min_periods=1).std().fillna(0)

            self.log("高级特征工程完成")
            return df

        except Exception as e:
            self.log(f"特征工程时出错: {str(e)}")
            return df

    def balance_data_advanced(self, X, y):
        """高级数据平衡策略"""
        self.log("应用高级数据平衡策略...")

        try:
            # 检查类别分布
            class_counts = pd.Series(y).value_counts().sort_index()
            min_class_count = class_counts.min()

            # 计算目标分布
            majority_class_count = class_counts.max()

            # 使用更智能的采样策略
            if min_class_count <= 5:
                # 对极少类别，不使用SMOTE
                self.log("检测到极少类别，使用基础过采样策略")

                # 创建平衡数据集，确保每个类别至少有一定数量的样本
                min_samples = min(50, majority_class_count // 10)

                X_list = []
                y_list = []

                for class_label in np.unique(y):
                    class_mask = y == class_label
                    class_X = X[class_mask]
                    class_y = y[class_mask]

                    if len(class_y) < min_samples:
                        # 对少数类进行重复采样
                        multiplier = (min_samples // len(class_y)) + 1
                        class_X = np.repeat(class_X, multiplier, axis=0)[:min_samples]
                        class_y = np.repeat(class_y, multiplier, axis=0)[:min_samples]

                    X_list.append(class_X)
                    y_list.append(class_y)

                X_balanced = np.vstack(X_list)
                y_balanced = np.concatenate(y_list)

                # 随机打乱
                indices = np.random.permutation(len(y_balanced))
                X_balanced = X_balanced[indices]
                y_balanced = y_balanced[indices]

            else:
                # 使用SMOTE + 下采样的组合策略
                # 设置合适的k_neighbors
                n_neighbors = min(5, min_class_count - 1) if min_class_count > 1 else 1

                # 过采样策略：确保每个类别至少有足够的样本
                over_sampling_strategy = {}
                for class_label in np.unique(y):
                    if class_counts[class_label] < majority_class_count * 0.2:
                        over_sampling_strategy[class_label] = int(majority_class_count * 0.2)

                if over_sampling_strategy:
                    over = SMOTE(sampling_strategy=over_sampling_strategy,
                                 random_state=42,
                                 k_neighbors=n_neighbors)
                    X_over, y_over = over.fit_resample(X, y)
                else:
                    X_over, y_over = X, y

                # 下采样策略：防止数据集过大
                under_sampling_strategy = {}
                for class_label in np.unique(y_over):
                    if class_counts[class_label] > majority_class_count * 0.8:
                        under_sampling_strategy[class_label] = int(majority_class_count * 0.8)

                if under_sampling_strategy:
                    under = RandomUnderSampler(sampling_strategy=under_sampling_strategy,
                                               random_state=42)
                    X_balanced, y_balanced = under.fit_resample(X_over, y_over)
                else:
                    X_balanced, y_balanced = X_over, y_over

            # 记录平衡后的分布
            balanced_counts = pd.Series(y_balanced).value_counts().sort_index()
            self.log("平衡后的类别分布:")
            for class_id, count in balanced_counts.items():
                class_name = self.encoded_workstate_mapping.get(class_id, f"未知({class_id})")
                self.log(f"  {class_id} - {class_name}: {count}条 ({count / len(y_balanced) * 100:.1f}%)")

            return X_balanced, y_balanced

        except Exception as e:
            self.log(f"{str(e)}")
            return X, y

    def prepare_data(self, df, test_size=0.3, random_state=42):
        """准备训练和测试数据"""
        self.log(f"准备数据集 (测试集比例: {test_size:.1%})")

        try:
            # 排除时间列
            drop_columns = ["创建时间", "远程服务器时间"]
            existing_drop_columns = [col for col in drop_columns if col in df.columns]

            # 标签编码
            df = self.encode_labels(df)

            # 应用高级特征工程
            df = self.apply_advanced_feature_engineering(df)

            # 分离特征和标签
            X = df.drop(columns=["工况"] + existing_drop_columns)
            y = df["工况"]

            # 记录特征列表
            self.feature_columns = X.columns.tolist()
            self.log(f"特征数量: {len(self.feature_columns)}")

            # 标准化特征
            X_scaled = self.scaler.fit_transform(X)

            # 分割训练集和测试集
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=test_size, random_state=random_state, stratify=y
            )

            # 应用高级数据平衡策略
            X_train_balanced, y_train_balanced = self.balance_data_advanced(X_train, y_train)

            self.log(f"训练集大小: {X_train_balanced.shape[0]}样本")
            self.log(f"测试集大小: {X_test.shape[0]}样本")

            return X_train_balanced, X_test, y_train_balanced, y_test

        except Exception as e:
            self.log(f"准备数据时出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            raise

    def train_model_with_hyperparameter_tuning(self, X_train, y_train, X_test, y_test):
        """训练模型并进行超参数调优"""
        self.log("开始训练LightGBM模型，并进行超参数调优")

        try:
            num_classes = len(np.unique(y_train))
            self.log(f"类别数量: {num_classes}")

            # 优化后的超参数
            params = {
                "objective": "multiclass",
                "num_class": num_classes,
                "metric": ["multi_logloss", "multi_error"],
                "boosting_type": "gbdt",
                "max_depth": 8,  # 增加深度捕捉复杂模式
                "num_leaves": 255,  # 增加叶子节点
                "learning_rate": 0.01,  # 降低学习率提高精度
                "feature_fraction": 0.9,  # 使用更多特征
                "bagging_fraction": 0.9,
                "bagging_freq": 5,
                "min_data_in_leaf": 3,  # 减少最小叶节点样本数
                "min_child_weight": 0.001,
                "min_split_gain": 0.01,
                "reg_alpha": 0.01,  # 减少正则化以提高拟合能力
                "reg_lambda": 0.01,
                "verbosity": -1,
                "is_unbalance": True,  # 处理类别不平衡
                "seed": 42
            }

            # 准备数据集
            train_data = lgb.Dataset(X_train, label=y_train)
            valid_data = lgb.Dataset(X_test, label=y_test, reference=train_data)

            # 记录训练历史
            self.training_history = []

            def callback(env):
                iteration = env.iteration

                # 评估性能
                y_train_pred = np.argmax(env.model.predict(X_train), axis=1)
                train_acc = accuracy_score(y_train, y_train_pred)

                y_test_pred = np.argmax(env.model.predict(X_test), axis=1)
                test_acc = accuracy_score(y_test, y_test_pred)
                test_precision = precision_score(y_test, y_test_pred, average='macro', zero_division=0)
                test_recall = recall_score(y_test, y_test_pred, average='macro', zero_division=0)
                test_f1 = f1_score(y_test, y_test_pred, average='macro', zero_division=0)

                result = {
                    'iteration': iteration,
                    'train_acc': train_acc,
                    'test_acc': test_acc,
                    'test_precision': test_precision,
                    'test_recall': test_recall,
                    'test_f1': test_f1
                }
                self.training_history.append(result)

                if iteration % 50 == 0 or iteration == 999:
                    self.log(f"轮次 {iteration + 1}: "
                             f"训练集准确率={train_acc:.4f}, "
                             f"测试集准确率={test_acc:.4f}, "
                             f"F1={test_f1:.4f}")

            # 训练模型
            start_time = time.time()

            # 使用更多训练轮数
            early_stopping_callback = lgb.early_stopping(stopping_rounds=100, verbose=False)

            self.model = lgb.train(
                params,
                train_data,
                num_boost_round=1000,  # 增加训练轮数
                valid_sets=[valid_data],
                callbacks=[callback, early_stopping_callback]
            )

            training_time = time.time() - start_time
            self.log(f"模型训练完成! 总耗时: {training_time:.2f}秒，最佳迭代次数: {self.model.best_iteration}")

            return self.model

        except Exception as e:
            self.log(f"训练模型时出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            return None

    def evaluate_model(self, X_test, y_test):
        """评估模型性能"""
        self.log("评估模型性能")

        if self.model is None:
            self.log("错误: 模型未训练成功，无法评估")
            return None

        try:
            # 预测
            y_pred_proba = self.model.predict(X_test)
            y_pred = np.argmax(y_pred_proba, axis=1)

            # 计算指标
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='macro', zero_division=0)
            recall = recall_score(y_test, y_pred, average='macro', zero_division=0)
            f1 = f1_score(y_test, y_pred, average='macro', zero_division=0)

            self.log(f"测试集性能指标:")
            self.log(f"  准确率 (Accuracy): {accuracy:.4f}")
            self.log(f"  精确率 (Precision): {precision:.4f}")
            self.log(f"  召回率 (Recall): {recall:.4f}")
            self.log(f"  F1分数 (F1-Score): {f1:.4f}")

            # 分类报告
            report = classification_report(y_test, y_pred, digits=4, zero_division=0)
            self.log("\n分类报告:\n" + report)

            # 混淆矩阵
            cm = confusion_matrix(y_test, y_pred)
            self.confusion_matrix = cm

            # 原始标签评估
            y_test_original = np.array([self.encoded_to_original_map.get(label, label) for label in y_test])
            y_pred_original = np.array([self.encoded_to_original_map.get(label, label) for label in y_pred])

            accuracy_original = accuracy_score(y_test_original, y_pred_original)
            f1_original = f1_score(y_test_original, y_pred_original, average='macro', zero_division=0)

            self.log(f"\n基于原始标签的性能指标:")
            self.log(f"  准确率: {accuracy_original:.4f}")
            self.log(f"  F1分数: {f1_original:.4f}")

            return {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'confusion_matrix': cm,
                'classification_report': report,
                'y_pred': y_pred,
                'accuracy_original': accuracy_original,
                'f1_original': f1_original
            }

        except Exception as e:
            self.log(f"评估模型时出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            return None

    def plot_training_history(self):
        """绘制训练历史"""
        self.log("生成训练历史图表")

        if not self.training_history:
            self.log("警告: 没有训练历史记录可绘制")
            return

        try:
            # 提取数据
            iterations = [r['iteration'] + 1 for r in self.training_history]
            train_acc = [r['train_acc'] for r in self.training_history]
            test_acc = [r['test_acc'] for r in self.training_history]
            test_f1 = [r['test_f1'] for r in self.training_history]

            # 创建图表
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

            # 准确率曲线
            ax1.plot(iterations, train_acc, 'b-', label='训练集准确率', linewidth=2)
            ax1.plot(iterations, test_acc, 'r-', label='测试集准确率', linewidth=2)
            ax1.set_xlabel('迭代次数')
            ax1.set_ylabel('准确率')
            ax1.set_title('训练过程 - 准确率')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # F1分数曲线
            ax2.plot(iterations, test_f1, 'g-', label='测试集F1分数', linewidth=2)
            ax2.set_xlabel('迭代次数')
            ax2.set_ylabel('F1分数')
            ax2.set_title('训练过程 - F1分数')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            plt.tight_layout()

            output_path = os.path.join(self.output_dir, 'training_history.png')
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()

            self.log(f"训练历史图表已保存为 '{output_path}'")

        except Exception as e:
            self.log(f"绘制训练历史时出错: {str(e)}")

    def plot_confusion_matrix(self):
        """绘制混淆矩阵"""
        self.log("生成混淆矩阵图表")

        if not hasattr(self, 'confusion_matrix'):
            self.log("错误: 请先调用evaluate_model方法")
            return

        try:
            # 归一化混淆矩阵
            cm_normalized = self.confusion_matrix.astype('float') / self.confusion_matrix.sum(axis=1)[:, np.newaxis]

            # 创建标签
            class_labels = [f"{i}-{self.encoded_workstate_mapping.get(i, '未知')}" for i in
                            range(len(self.confusion_matrix))]

            # 绘制图表
            plt.figure(figsize=(10, 8))
            sns.heatmap(cm_normalized, annot=True, fmt='.2f', cmap='Blues',
                        xticklabels=class_labels, yticklabels=class_labels)
            plt.title('归一化混淆矩阵')
            plt.xlabel('预测标签')
            plt.ylabel('真实标签')
            plt.xticks(rotation=45)
            plt.yticks(rotation=45)
            plt.tight_layout()

            output_path = os.path.join(self.output_dir, 'confusion_matrix.png')
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()

            self.log(f"混淆矩阵已保存为 '{output_path}'")

        except Exception as e:
            self.log(f"绘制混淆矩阵时出错: {str(e)}")

    def plot_feature_importance(self, top_n=20):
        """绘制特征重要性"""
        self.log("生成特征重要性图表")

        if not self.model:
            self.log("错误: 模型未训练")
            return

        try:
            # 获取特征重要性
            importance = self.model.feature_importance(importance_type='gain')

            feature_importance = pd.DataFrame({
                'Feature': self.feature_columns,
                'Importance': importance
            }).sort_values(by='Importance', ascending=False)

            # 选择top N
            top_features = feature_importance.head(top_n)

            # 绘制图表
            plt.figure(figsize=(10, 12))
            ax = sns.barplot(x='Importance', y='Feature', data=top_features)

            for i, v in enumerate(top_features['Importance']):
                ax.text(v + 0.1, i, f"{v:.1f}", va='center')

            plt.title(f'特征重要性 (Top {len(top_features)})')
            plt.xlabel('重要性')
            plt.ylabel('特征')
            plt.tight_layout()

            output_path = os.path.join(self.output_dir, 'feature_importance.png')
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()

            self.log(f"特征重要性图表已保存为 '{output_path}'")

            return feature_importance

        except Exception as e:
            self.log(f"绘制特征重要性时出错: {str(e)}")
            return None

    def save_model(self, model_path=None):
        """保存模型"""
        if model_path is None:
            model_path = os.path.join(self.output_dir, "lightgbm_model.txt")

        self.log(f"保存模型到: {model_path}")

        if not self.model:
            self.log("错误: 模型未训练")
            return False

        try:
            # 保存模型
            self.model.save_model(model_path)

            # 保存标签映射 - 确保所有数值都是标准Python类型
            mapping_path = os.path.join(self.output_dir, "label_mapping.json")
            import json

            mapping_data = {
                'original_to_encoded': {str(k): int(v) for k, v in self.original_to_encoded_map.items()},
                'encoded_to_original': {str(k): int(v) for k, v in self.encoded_to_original_map.items()},
                'workstate_mapping': {str(k): str(v) for k, v in self.workstate_mapping.items()},
                'encoded_workstate_mapping': {str(k): str(v) for k, v in self.encoded_workstate_mapping.items()}
            }

            with open(mapping_path, 'w', encoding='utf-8') as f:
                json.dump(mapping_data, f, ensure_ascii=False, indent=4)

            self.log(f"模型保存成功: {model_path}")
            self.log(f"标签映射保存成功: {mapping_path}")
            return True
        except Exception as e:
            self.log(f"错误: 模型保存失败 - {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            return False

    def save_training_log(self, log_path=None):
        """保存训练日志"""
        if log_path is None:
            log_path = os.path.join(self.output_dir, "training_log.txt")

        self.log(f"保存训练日志到: {log_path}")

        try:
            with open(log_path, "w", encoding="utf-8") as f:
                for entry in self.log_entries:
                    f.write(entry + "\n")
            self.log(f"训练日志保存成功: {log_path}")
            return True
        except Exception as e:
            self.log(f"错误: 训练日志保存失败 - {str(e)}")
            return False

    def run_pipeline(self, data_path="optimized_data.csv", test_size=0.3):
        """执行完整训练流程"""
        self.log("开始钻井工况识别模型优化训练流程")

        try:
            # 加载数据
            df = self.load_data(data_path)
            if df is None:
                return False

            # 准备数据
            X_train, X_test, y_train, y_test = self.prepare_data(df, test_size=test_size, random_state=42)

            # 训练模型
            self.train_model_with_hyperparameter_tuning(X_train, y_train, X_test, y_test)

            # 评估模型
            eval_results = self.evaluate_model(X_test, y_test)

            if not eval_results:
                self.log("模型评估失败")
                return False

            # 生成图表
            self.plot_training_history()
            self.plot_confusion_matrix()
            self.plot_feature_importance()

            # 保存模型和日志
            self.save_model()
            self.save_training_log()

            self.log("训练流程完成!")
            return eval_results

        except Exception as e:
            self.log(f"执行训练流程时出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            return False


if __name__ == "__main__":
    # 创建钻井工况识别器
    classifier = DrillingConditionIdentifier(output_dir="drilling_results")

    try:
        # 执行训练流程
        results = classifier.run_pipeline(
            data_path="optimized_data.csv",
            test_size=0.2
        )

        if results:
            print("\n" + "=" * 60)
            print("钻井工况识别模型训练完成!")
            print(f"最终准确率: {results['accuracy']:.4f}")
            print(f"最终F1分数: {results['f1']:.4f}")
            print(f"原始标签准确率: {results['accuracy_original']:.4f}")
            print(f"原始标签F1分数: {results['f1_original']:.4f}")

            # 额外的性能信息
            print(f"精确率: {results['precision']:.4f}")
            print(f"召回率: {results['recall']:.4f}")

            print(f"\n所有输出文件已保存到: {classifier.output_dir}")
            print("=" * 60)

    except Exception as e:
        print(f"\n错误: {str(e)}")
        import traceback

        traceback.print_exc()