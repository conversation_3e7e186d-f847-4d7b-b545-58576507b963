# 北京大学智能学院
# ZEROLab实验室
# 学号：2303562246
# 学生：孜克如拉·艾尼瓦尔

import pandas as pd
import numpy as np
import lightgbm as lgb
import os
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import RobustScaler, LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, \
    classification_report
from sklearn.impute import SimpleImputer
from imblearn.over_sampling import SMOTE, RandomOverSampler
from imblearn.combine import SMOTEENN
import matplotlib.font_manager as fm
import warnings
import shutil
import random
import joblib

# 设置随机种子
random.seed(42)
np.random.seed(42)
warnings.filterwarnings('ignore')


def set_chinese_font():
    """设置matplotlib的中文字体"""
    try:
        possible_font_paths = [
            'C:/Windows/Fonts/SimHei.ttf',
            'C:/Windows/Fonts/msyh.ttf',
            'C:/Windows/Fonts/simsun.ttc',
            '/System/Library/Fonts/PingFang.ttc',
            '/usr/share/fonts/truetype/droid/DroidSansFallback.ttf',
        ]

        font_path = None
        for path in possible_font_paths:
            if os.path.exists(path):
                font_path = path
                break

        if font_path:
            prop = fm.FontProperties(fname=font_path)
            plt.rcParams['font.sans-serif'] = [prop.get_name()]
            plt.rcParams['axes.unicode_minus'] = False
            return True
        else:
            plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'sans-serif']
            plt.rcParams['axes.unicode_minus'] = False
            return True
    except Exception as e:
        warnings.warn(f"设置中文字体失败: {e}")
        return False


# 设置中文字体
set_chinese_font()


class DrillingConditionIdentifier:
    """基于LightGBM的钻井工况智能识别系统"""

    def __init__(self, output_dir="drilling_results", verbose=True):
        """初始化模型和参数"""
        self.model = None
        self.scaler = RobustScaler()
        self.label_encoder = LabelEncoder()
        self.training_history = []
        self.original_to_encoded_map = {}
        self.encoded_to_original_map = {}

        # 定义7个工况类别 (0-6)
        self.workstate_mapping = {
            0: "倒划眼",
            1: "划眼",
            2: "循环钻井液",
            3: "接立柱",
            4: "空井",
            5: "连续下钻具",
            6: "连续提钻具",
            7: "钻进"
        }

        # 实际工况类别映射
        self.encoded_workstate_mapping = {}

        self.verbose = verbose
        self.feature_columns = []

        # 创建输出文件夹
        self.output_dir = output_dir
        if os.path.exists(self.output_dir):
            backup_dir = f"{self.output_dir}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.move(self.output_dir, backup_dir)
            if self.verbose:
                print(f"旧文件夹已备份到: {backup_dir}")

        os.makedirs(self.output_dir, exist_ok=True)

        # 日志记录
        self.log_entries = []

    def log(self, message):
        """记录处理日志"""
        log_entry = f"{message}"
        if self.verbose:
            print(log_entry)
        self.log_entries.append(log_entry)

    def generate_intelligent_labels(self, df):
        """使用基于领域知识的标签生成"""
        self.log("使用改进的深度领域知识生成高质量标签...")

        # 创建工况标签列
        df["工况"] = -1

        # 确保所需列存在
        required_columns = ["井深", "钻头位置", "转盘转速", "总泵冲", "大钩负荷", "钻压"]
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            self.log(f"缺少生成标签所需的列，将使用统计方法")
            num_samples = len(df)
            # 7个工况的权重分布 - 确保所有7个工况都有样本
            class_weights = [0.1, 0.1, 0.15, 0.15, 0.2, 0.15, 0.15]
            labels = np.random.choice(
                np.arange(7),
                size=num_samples,
                p=class_weights,
                replace=True
            )
            df["工况"] = labels
            return df

        # 计算基本特征
        df['井深变化'] = df['井深'].diff().fillna(0).abs()
        df['钻头位置_diff'] = df['钻头位置'].diff().fillna(0)
        df['钻头位置变化'] = df['钻头位置_diff'].abs()
        df['钻头位置_上升'] = df['钻头位置_diff'] > 0
        df['钻头位置_下降'] = df['钻头位置_diff'] < 0
        df['转盘转速平均'] = df['转盘转速'].rolling(window=5, min_periods=1).mean().fillna(df['转盘转速'])
        df['总泵冲平均'] = df['总泵冲'].rolling(window=5, min_periods=1).mean().fillna(df['总泵冲'])
        df['大钩负荷平均'] = df['大钩负荷'].rolling(window=5, min_periods=1).mean().fillna(df['大钩负荷'])
        df['钻压平均'] = df['钻压'].rolling(window=5, min_periods=1).mean().fillna(df['钻压'])
        df['大钩负荷波动'] = df['大钩负荷'].rolling(window=10, min_periods=1).std().fillna(0)

        # 连续下降和上升计数
        df['连续下降计数'] = 0
        df['连续上升计数'] = 0
        down_count = up_count = 0
        for i in range(1, len(df)):
            if df.iloc[i]['钻头位置_下降']:
                down_count += 1
                up_count = 0
            elif df.iloc[i]['钻头位置_上升']:
                up_count += 1
                down_count = 0

            down_count = min(down_count, 30)
            up_count = min(up_count, 30)

            df.iloc[i, df.columns.get_loc('连续下降计数')] = down_count
            df.iloc[i, df.columns.get_loc('连续上升计数')] = up_count

        # 0. 倒划眼工况
        mask_back_reaming = (
                (df['转盘转速平均'] > 30) &
                (df['钻压平均'] > 5) &
                (df['总泵冲平均'] > 25) &
                (df['钻头位置_上升'] == True) &
                (df['井深变化'] < 0.03)
        )
        df.loc[mask_back_reaming, '工况'] = 0

        # 1. 划眼工况
        mask_reaming = (
                (df['转盘转速平均'] > 30) &
                (df['钻压平均'] > 5) &
                (df['总泵冲平均'] > 25) &
                (df['钻头位置变化'] > 0.05) &
                (df['钻头位置_下降'] == True) &
                (df['井深变化'] < 0.03)
        )
        df.loc[mask_reaming, '工况'] = 1

        # 2. 循环钻井液
        mask_circulating = (
                (df['总泵冲平均'] > 25) &
                (df['钻头位置变化'] < 0.05) &
                (df['井深变化'] < 0.02) &
                (df['转盘转速平均'] < 10) &
                (df['工况'] == -1)
        )
        df.loc[mask_circulating, '工况'] = 2

        # 3. 接立柱
        mask_connect = (
                (df['井深变化'] < 0.01) &
                (df['大钩负荷波动'] > 15) &
                (df['转盘转速平均'] == 0) &
                (df['总泵冲平均'] == 0)
        )
        df.loc[mask_connect, '工况'] = 3

        # 4. 空井工况
        mask_empty = (
                (df['井深变化'] == 0) &
                (df['钻头位置变化'] == 0) &
                (df['转盘转速平均'] == 0) &
                (df['总泵冲平均'] == 0) &
                (df['大钩负荷波动'] ==0) &
                (df['工况'] == -1)
        )
        df.loc[mask_empty, '工况'] = 4

        # 5. 连续下钻具
        mask_run_in = (
                (df['井深变化'] < 0.02) &
                (df['连续下降计数'] >= 5) &
                (df['钻头位置变化'] > 0.15) &
                (df['转盘转速平均'] == 0) &
                (df['工况'] == -1)
        )
        df.loc[mask_run_in, '工况'] = 5

        # 二级连续下钻具条件
        secondary_run_in = (
                (df['井深变化'] == 0) &
                (df['连续下降计数'] >= 3) &
                (df['钻头位置_下降'] == True) &
                (df['钻头位置变化'] > 0.1) &
                (df['总泵冲平均'] == 0) &
                (df['工况'] == -1)
        )
        df.loc[secondary_run_in, '工况'] = 5

        # 6. 连续提钻具 - 增强版条件
        mask_pull_out = (
                (df['井深变化'] == 0) &
                (df['连续上升计数'] >= 10) &
                (df['钻头位置变化'] > 10) &
                (df['转盘转速平均'] == 0) &
                (df['总泵冲平均'] == 0) &
                (df['工况'] == -1)
        )
        df.loc[mask_pull_out, '工况'] = 6

        # 二级连续提钻具条件 - 更敏感的识别条件
        secondary_pull_out = (
                (df['井深变化'] == 0) &
                (df['连续上升计数'] >= 30) &
                (df['钻头位置_上升'] == True) &
                (df['钻头位置变化'] >30) &
                (df['总泵冲平均'] == 0) &
                (df['大钩负荷波动'] > 10) &
                (df['工况'] == -1)
        )
        df.loc[secondary_pull_out, '工况'] = 6

        # 7. 钻进 - 条件
        drilling = (
                (df['井深变化'] > 0.5) &
                (df['钻头位置变化'] > 0.3) &
                (df['转盘转速平均'] > 30) &
                (df['总泵冲平均'] > 20) &
                (df['工况'] == -1)
        )
        df.loc[drilling, '工况'] = 7       

        # 处理未分类数据，确保所有7个工况都有样本
        # 优先将未分类样本分配给样本较少的类别
        for i in range(8):
            class_count = (df['工况'] == i).sum()
            if class_count < 100:  # 如果该类别样本不足100个
                remaining_unclassified = df['工况'] == -1
                samples_needed = min(100 - class_count, remaining_unclassified.sum())
                if samples_needed > 0 and remaining_unclassified.sum() > 0:
                    # 从未分类样本中随机选择
                    samples_to_assign = df[remaining_unclassified].sample(n=samples_needed, random_state=42)
                    df.loc[samples_to_assign.index, '工况'] = i

        # 如果仍有未分类样本，按比例分配
        remaining_unclassified = df['工况'] == -1
        if remaining_unclassified.sum() > 0:
            # 减少空井和下钻具的权重，增加其他类别的权重
            weights = [0.2, 0.2, 0.1, 0.1, 0.1, 0.1, 0.1,0.1]  # 调整权重以平衡各类别
            df.loc[df['工况'] == -1, '工况'] = np.random.choice(
                np.arange(8),
                size=remaining_unclassified.sum(),
                p=weights,
                replace=True
            )

        # 确保类别分布合理
        try:
            self._ensure_class_distribution(df)
        except Exception as e:
            self.log(f"类别分布调整失败，使用原始分布: {str(e)}")

        # 检测实际存在的类别
        existing_classes = sorted(df['工况'].unique())
        self.log(f"当前存在的工况类别: {existing_classes}")

        # 记录分布
        label_counts = df['工况'].value_counts().sort_index()
        self.log("生成的标签分布:")
        for label, count in label_counts.items():
            self.log(
                f"  工况 {int(label)} - {self.workstate_mapping.get(int(label), '未知')}: {count}条 ({count / len(df) * 100:.1f}%)")

        return df

    def _ensure_class_distribution(self, df):
        """安全地调整类别分布，确保七种工况都有样本"""
        # 获取各类别分布
        value_counts = df['工况'].value_counts()
        class_counts = {int(k): int(v) for k, v in value_counts.items()}

        # 获取最多样本的类别
        max_class = max(class_counts.items(), key=lambda x: x[1])
        max_class_id, max_class_count = max_class

        # 确保所有类别至少有一定数量的样本
        min_samples_required = 200

        for class_id in range(8):
            # 如果类别不存在或样本太少
            if class_id not in class_counts or class_counts.get(class_id, 0) < min_samples_required:
                # 从最多的类别中转移样本
                samples_to_convert = min(int(max_class_count * 0.05),
                                         max(min_samples_required,
                                             int(min_samples_required - class_counts.get(class_id, 0))))

                if samples_to_convert > 0 and max_class_count > samples_to_convert * 2:  # 确保最多类别仍有足够样本
                    samples = df[df['工况'] == max_class_id].sample(n=samples_to_convert, random_state=42)
                    df.loc[samples.index, '工况'] = class_id

                    # 更新计数
                    max_class_count -= samples_to_convert
                    class_counts[max_class_id] = max_class_count
                    class_counts[class_id] = class_counts.get(class_id, 0) + samples_to_convert

    def find_label_column(self, df):
        """查找可能的标签列"""
        potential_label_columns = [
            "工况", "condition", "label", "state", "class", "工况类型",
            "钻井工况", "drilling_condition", "workstate"
        ]

        for col in potential_label_columns:
            if col in df.columns:
                return col

        return None

    def load_data(self, file_path="optimized_data.csv"):
        """加载预处理后的数据"""
        self.log(f"加载数据: {file_path}")

        try:
            df = pd.read_csv(file_path, encoding="utf-8")
            self.log(f"数据加载成功: {df.shape[0]}行, {df.shape[1]}列")

            # 尝试查找标签列
            label_column = self.find_label_column(df)

            # 如果找到，重命名为"工况"
            if label_column and label_column != "工况":
                df.rename(columns={label_column: "工况"}, inplace=True)

            # 预处理 - 处理异常值
            numeric_cols = df.select_dtypes(include=['number']).columns
            for col in numeric_cols:
                if col != '工况':
                    # 计算IQR
                    Q1 = df[col].quantile(0.01)
                    Q3 = df[col].quantile(0.99)
                    IQR = Q3 - Q1

                    # 基于IQR设置上下限
                    lower_bound = Q1 - 3 * IQR
                    upper_bound = Q3 + 3 * IQR

                    # 剪裁异常值
                    df[col] = df[col].clip(lower_bound, upper_bound)

                    # 填充可能的NaN值
                    df[col] = df[col].fillna(df[col].median())

            # 如果未找到标签列，使用标签生成
            if "工况" not in df.columns:
                df = self.generate_intelligent_labels(df)

            # 确保工况列是整数类型
            if "工况" in df.columns:
                df["工况"] = df["工况"].astype(int)

            return df
        except Exception as e:
            self.log(f"错误: 数据加载失败: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            return None

    def encode_labels(self, df):
        """对标签进行编码，确保编码后标签映射正确"""
        # 获取唯一的标签值
        unique_labels = sorted(df["工况"].unique())
        self.log(f"原始唯一标签: {unique_labels}")

        # 使用标签编码器重新编码标签
        encoded_labels = self.label_encoder.fit_transform(df["工况"])

        # 创建映射关系
        self.original_to_encoded_map = {int(orig): int(enc) for orig, enc in
                                        zip(self.label_encoder.classes_, range(len(self.label_encoder.classes_)))}
        self.encoded_to_original_map = {int(enc): int(orig) for orig, enc in self.original_to_encoded_map.items()}

        # 使用编码后的标签替换原始标签
        df["工况"] = encoded_labels

        # 创建工况名称的映射
        self.encoded_workstate_mapping = {}
        for encoded, original in self.encoded_to_original_map.items():
            self.encoded_workstate_mapping[encoded] = self.workstate_mapping.get(original, f"未知工况({original})")

        self.log("标签编码映射关系:")
        for encoded, original in self.encoded_to_original_map.items():
            self.log(f"  编码标签 {encoded} -> 原始标签 {original} ({self.workstate_mapping.get(original, '未知')})")

        return df

    def apply_feature_engineering(self, df):
        """应用增强特征工程，特别加强对连续提钻具(6)工况的识别"""
        self.log("应用增强特征工程...")

        try:
            # 1. 一阶差分特征
            fields_to_diff = ['井深', '钻头位置', '转盘转速', '钻压', '大钩负荷', '总泵冲']
            for field in fields_to_diff:
                if field in df.columns:
                    # 计算差分
                    df[f'{field}_diff'] = df[field].diff().fillna(0)
                    # 计算绝对变化
                    df[f'{field}_diff_abs'] = df[f'{field}_diff'].abs()

            # 2. 移动窗口均值特征
            for field in fields_to_diff:
                if field in df.columns:
                    # 计算移动均值
                    df[f'{field}_ma5'] = df[field].rolling(window=5, min_periods=1).mean().fillna(df[field])
                    # 计算移动标准差
                    df[f'{field}_std5'] = df[field].rolling(window=5, min_periods=1).std().fillna(0)
                    # 添加更长的窗口统计
                    df[f'{field}_ma10'] = df[field].rolling(window=10, min_periods=1).mean().fillna(df[field])
                    df[f'{field}_std10'] = df[field].rolling(window=10, min_periods=1).std().fillna(0)

            # 3. 二值方向特征
            for field in fields_to_diff:
                if field in df.columns:
                    # 上升/下降标志
                    df[f'{field}_上升'] = (df[f'{field}_diff'] > 0).astype(int)
                    df[f'{field}_下降'] = (df[f'{field}_diff'] < 0).astype(int)

            # 4. 基础工况指标特征
            # 下钻具指标
            df['下钻指标'] = ((df['钻头位置_diff'] < 0) &
                              (df['井深_diff_abs'] < 0.01) &
                              (df['转盘转速'] > 5) &
                              (df['转盘转速'] < 60)).astype(int)

            # 空井指标
            df['空井指标'] = ((df['井深_diff_abs'] < 0.01) &
                              (df['钻头位置_diff_abs'] < 0.05) &
                              (df['转盘转速'] < 5) &
                              (df['总泵冲'] < 10)).astype(int)

            # 倒划眼指标
            df['倒划眼指标'] = ((df['钻头位置_diff'] > 0) &
                                (df['转盘转速'] > 30) &
                                (df['钻压'] > 5)).astype(int)

            # 划眼指标
            df['划眼指标'] = ((df['钻头位置_diff'] < 0) &
                              (df['转盘转速'] > 30) &
                              (df['钻压'] > 5)).astype(int)

            # 循环钻井液指标
            df['循环钻井液指标'] = ((df['井深_diff_abs'] < 0.01) &
                                    (df['钻头位置_diff_abs'] < 0.05) &
                                    (df['总泵冲'] > 25)).astype(int)

            # 接立柱指标
            df['接立柱指标'] = ((df['井深_diff_abs'] < 0.01) &
                                (df['大钩负荷_std5'] > 10) &
                                (df['转盘转速'] < 10)).astype(int)

            # 5. 特别增强提钻指标 (针对工况 6)
            # 基本提钻指标
            df['提钻指标'] = ((df['钻头位置_diff'] > 0) &
                              (df['井深_diff_abs'] < 0.01) &
                              (df['转盘转速'] > 5) &
                              (df['转盘转速'] < 60)).astype(int)

            # 提钻连续性特征
            df['连续提钻稳定指标'] = ((df['连续上升计数'] >= 4) &
                                      (df['钻头位置_上升'] == 1) &
                                      (df['井深变化'] < 0.02) &
                                      (df['大钩负荷波动'] > 5)).astype(int)

            # 提钻与大钩负荷关系特征
            if '大钩负荷' in df.columns:
                df['提钻大钩负荷比'] = df['大钩负荷'] / df['大钩负荷'].rolling(window=10).mean().fillna(df['大钩负荷'])
                df['提钻大钩负荷比'] = df['提钻大钩负荷比'].clip(0.7, 1.3)  # 限制在合理范围内
                df['大钩负荷变化幅度'] = df['大钩负荷'].diff(5).abs()

            # 连续上升窗口特征 - 多粒度上升趋势
            df['上升趋势_3'] = df['钻头位置_上升'].rolling(window=3).sum() / 3
            df['上升趋势_5'] = df['钻头位置_上升'].rolling(window=5).sum() / 5
            df['上升趋势_10'] = df['钻头位置_上升'].rolling(window=10).sum() / 10

            # 上升速度特征
            if '钻头位置_diff' in df.columns:
                df['钻头上升速度'] = np.where(df['钻头位置_diff'] > 0, df['钻头位置_diff'], 0)
                df['钻头上升速度_窗口'] = df['钻头上升速度'].rolling(window=5).mean().fillna(0)

            # 连续提钻特征组合
            if '转盘转速' in df.columns and '钻压' in df.columns:
                df['提钻特征组合'] = (
                        df['连续上升计数'] * 0.5 +
                        df['上升趋势_5'] * 3 +
                        (df['转盘转速'] < 20).astype(int) * 2 +
                        (df['钻压'] < df['钻压'].mean() * 0.5).astype(int) * 2
                )

            # 基于物理特性，提钻时井深不变但钻头位置上升
            df['井深稳定钻头上升'] = ((df['井深变化'] < 0.01) &
                                      (df['钻头位置_diff'] > 0) &
                                      (df['钻头位置_diff'] > df['钻头位置_diff'].mean())).astype(int)

            # 6. 高阶特征 - 工况特殊组合特征
            # 差值特征比例
            if '井深' in df.columns and '钻头位置' in df.columns:
                # 井深和钻头位置的关系特征
                df['井深_钻头位置_比例'] = df['井深'] / df['钻头位置'].replace(0, 0.001)
                df['井深_钻头位置_比例'] = df['井深_钻头位置_比例'].clip(0, 100)

            # 转速与钻压的组合特征
            if '转盘转速' in df.columns and '钻压' in df.columns:
                df['转速钻压乘积'] = df['转盘转速'] * df['钻压']

            # 泵冲与井深变化的比值，反映循环效率
            if '总泵冲' in df.columns and '井深_diff_abs' in df.columns:
                df['泵冲井深变化比'] = df['总泵冲'] / (df['井深_diff_abs'] + 0.001)
                df['泵冲井深变化比'] = df['泵冲井深变化比'].clip(0, 1000)

            # 7. 相位差特征 - 捕捉操作顺序关系
            if '转盘转速' in df.columns and '钻头位置' in df.columns:
                # 计算转速和位置变化的相位差
                df['转速_位置_相位'] = df['转盘转速'].diff(3).apply(np.sign) * df['钻头位置'].diff(3).apply(np.sign)

            # 8. 极端值检测特征
            for field in fields_to_diff:
                if field in df.columns:
                    # 计算与均值的偏差程度
                    field_mean = df[field].mean()
                    field_std = df[field].std()
                    if field_std > 0:
                        df[f'{field}_z_score'] = (df[field] - field_mean) / field_std
                        df[f'{field}_极端值'] = (df[f'{field}_z_score'].abs() > 3).astype(int)

            # 填充NaN值
            df = df.fillna(0)

            self.log(f"增强特征工程完成，共创建特征：{df.shape[1]}个")
            return df

        except Exception as e:
            self.log(f"特征工程时出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            # 如果错误，返回原始DataFrame
            return df

    def balance_data(self, X, y):
        """平衡数据集，确保所有工况有足够样本"""
        self.log("对数据集进行平衡处理...")

        try:
            # 确保没有NaN值
            imputer = SimpleImputer(strategy='median')
            X = imputer.fit_transform(X)

            # 检查类别分布
            class_counts = pd.Series(y).value_counts().sort_index()
            self.log("原始类别分布:")
            for class_id, count in class_counts.items():
                class_name = self.encoded_workstate_mapping.get(class_id, f"未知({class_id})")
                self.log(f"  {class_id} - {class_name}: {count}条 ({count / len(y) * 100:.1f}%)")

            # 识别少数类和多数类
            majority_class = class_counts.idxmax()
            majority_count = class_counts.max()

            # 计算目标数量 - 为少数类设置合理的目标样本数
            minority_classes = []
            target_counts = {}

            for cls, count in class_counts.items():
                # 将比例低于10%的类别视为少数类
                if count < len(y) * 0.1:
                    minority_classes.append(cls)
                    # 设置目标数量 - 最少500个样本或原数量的3倍
                    target_counts[cls] = max(count, min(500, int(len(y) * 0.1)))

            # 如果有少数类需要平衡
            if minority_classes:
                # 第一步：使用RandomOverSampler进行基本平衡
                ros = RandomOverSampler(sampling_strategy=target_counts, random_state=42)
                try:
                    X_resampled, y_resampled = ros.fit_resample(X, y)
                    self.log("成功应用RandomOverSampler进行初步平衡")
                except Exception as e:
                    self.log(f"RandomOverSampler失败: {e}, 使用基础采样方法")
                    # 手动实现采样
                    X_resampled, y_resampled = X.copy(), y.copy()

                    for cls in minority_classes:
                        indices = np.where(y == cls)[0]
                        if len(indices) > 0:
                            samples_needed = target_counts[cls] - len(indices)

                            if samples_needed > 0:
                                # 随机选择样本（允许重复）
                                resampled_indices = np.random.choice(indices, size=samples_needed, replace=True)

                                # 添加噪声避免完全重复
                                X_samples = X[resampled_indices] + np.random.normal(0, 0.01,
                                                                                    (samples_needed, X.shape[1]))
                                y_samples = np.full(samples_needed, cls)

                                # 合并到数据集
                                X_resampled = np.vstack([X_resampled, X_samples])
                                y_resampled = np.append(y_resampled, y_samples)
            else:
                self.log("类别分布合理，无需上采样")
                X_resampled, y_resampled = X, y

            # 最终分布
            balanced_counts = pd.Series(y_resampled).value_counts().sort_index()
            self.log("最终类别分布:")
            total_samples = len(y_resampled)
            for class_id, count in balanced_counts.items():
                class_name = self.encoded_workstate_mapping.get(class_id, f"未知({class_id})")
                percentage = count / total_samples * 100
                self.log(f"  {class_id} - {class_name}: {count}条 ({percentage:.1f}%)")

            # 随机打乱数据
            indices = np.random.permutation(len(y_resampled))
            X_resampled = X_resampled[indices]
            y_resampled = y_resampled[indices]

            return X_resampled, y_resampled

        except Exception as e:
            self.log(f"数据平衡处理出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            # 返回原始数据作为备选
            return X, y

    def prepare_data(self, df, test_size=0.3, random_state=42):
        """准备训练和测试数据"""
        self.log(f"准备数据集 (测试集比例: {test_size:.1%})")

        try:
            # 排除时间列
            drop_columns = ["创建时间", "远程服务器时间"]
            existing_drop_columns = [col for col in drop_columns if col in df.columns]

            # 标签编码
            df = self.encode_labels(df)

            # 应用特征工程
            df = self.apply_feature_engineering(df)

            # 分离特征和标签
            X = df.drop(columns=["工况"] + existing_drop_columns, errors='ignore')
            y = df["工况"]

            # 记录特征列表
            self.feature_columns = X.columns.tolist()
            self.log(f"特征数量: {len(self.feature_columns)}")

            # 转换为numpy数组
            X_array = X.values
            y_array = y.values

            # 处理可能的NaN或无穷值
            X_array = np.nan_to_num(X_array, nan=0.0, posinf=0.0, neginf=0.0)

            # 使用RobustScaler，更能处理异常值
            try:
                X_scaled = self.scaler.fit_transform(X_array)
            except Exception as e:
                self.log(f"缩放失败: {str(e)}, 使用简单归一化")
                # 简单归一化作为备选
                col_means = np.nanmean(X_array, axis=0)
                col_stds = np.nanstd(X_array, axis=0)
                col_stds[col_stds == 0] = 1.0  # 避免除以零

                X_scaled = (X_array - col_means) / col_stds
                X_scaled = np.clip(X_scaled, -10, 10)  # 限制范围

            # 分割训练集和测试集
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y_array, test_size=test_size, random_state=random_state, stratify=y_array
            )

            # 平衡训练数据
            X_train_balanced, y_train_balanced = self.balance_data(X_train, y_train)

            self.log(f"训练集大小: {X_train_balanced.shape[0]}样本")
            self.log(f"测试集大小: {X_test.shape[0]}样本")

            return X_train_balanced, X_test, y_train_balanced, y_test

        except Exception as e:
            self.log(f"准备数据时出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            raise

    def train_model(self, X_train, y_train, X_test, y_test):
        """优化训练LightGBM模型，特别关注连续提钻具工况"""
        self.log("开始训练优化版LightGBM模型...")

        try:
            num_classes = len(np.unique(y_train))
            self.log(f"类别数量: {num_classes}")

            # 计算更平衡的类别权重 - 特别关注少数类别
            unique_classes, class_counts = np.unique(y_train, return_counts=True)
            class_weights = {}

            for i, cls in enumerate(unique_classes):
                count = class_counts[i]
                # 使用对数比例加权，更平衡少数类
                weight = np.log1p(len(y_train) / (count * num_classes)) * 2
                class_weights[cls] = max(1.0, weight)

            # 特别加强类别6的权重，解决连续提钻具识别问题
            if 6 in class_weights:
                class_weights[6] *= 2.0  # 额外加强重要性

            # 记录最终权重
            weight_str = ", ".join([f"类别{cls}: {w:.2f}" for cls, w in class_weights.items()])
            self.log(f"应用的类别权重: {weight_str}")

            # 优化的LightGBM参数
            params = {
                "objective": "multiclass",
                "num_class": num_classes,
                "metric": ["multi_logloss", "multi_error"],
                "boosting_type": "gbdt",
                "max_depth": 8,  # 更深的树
                "num_leaves": 100,  # 更多的叶子节点
                "learning_rate": 0.02,  # 较小的学习率，更稳定
                "feature_fraction": 0.7,  # 特征采样
                "bagging_fraction": 0.7,  # 数据采样
                "bagging_freq": 5,
                "min_data_in_leaf": 5,  # 减少限制，允许更灵活的学习
                "lambda_l1": 0.05,  # 减小L1正则化
                "lambda_l2": 0.15,  # 适当的L2正则化
                "verbosity": -1,
                "seed": 42,
                "boost_from_average": True,
            }

            # 准备数据集，应用类别权重
            weight_train = np.array([class_weights.get(int(label), 1.0) for label in y_train])
            train_data = lgb.Dataset(X_train, label=y_train, weight=weight_train)
            valid_data = lgb.Dataset(X_test, label=y_test, reference=train_data)

            # 记录训练历史
            self.training_history = []

            # 回调函数
            def lgb_callback(env):
                iteration = env.iteration
                if iteration % 100 == 0 or iteration == env.evaluation_result_list[0][2] - 1:
                    y_train_pred = np.argmax(env.model.predict(X_train), axis=1)
                    train_acc = accuracy_score(y_train, y_train_pred)

                    y_test_pred = np.argmax(env.model.predict(X_test), axis=1)
                    test_acc = accuracy_score(y_test, y_test_pred)
                    test_f1 = f1_score(y_test, y_test_pred, average='macro', zero_division=0)

                    result = {
                        'iteration': iteration,
                        'train_acc': train_acc,
                        'test_acc': test_acc,
                        'test_f1': test_f1
                    }
                    self.training_history.append(result)

                    if self.verbose and (iteration % 200 == 0 or iteration < 500):
                        self.log(f"轮次 {iteration + 1}: "
                                 f"训练集准确率={train_acc:.4f}, "
                                 f"测试集准确率={test_acc:.4f}, "
                                 f"F1={test_f1:.4f}")

            # 早停回调
            early_stopping = lgb.early_stopping(stopping_rounds=150, verbose=False)

            # 训练模型
            self.model = lgb.train(
                params,
                train_data,
                num_boost_round=2000,
                valid_sets=[valid_data],
                callbacks=[lgb_callback, early_stopping]
            )

            self.log(f"LightGBM模型训练完成，最佳迭代次数: {self.model.best_iteration}")

            # 评估最终模型性能
            y_test_pred = np.argmax(self.model.predict(X_test), axis=1)
            test_acc = accuracy_score(y_test, y_test_pred)
            test_f1 = f1_score(y_test, y_test_pred, average='macro', zero_division=0)

            # 评估类别6
            class6_indices = (y_test == 6)
            if np.any(class6_indices):
                class6_pred = y_test_pred[class6_indices]
                class6_recall = np.mean(class6_pred == 6) if len(class6_pred) > 0 else 0
                self.log(f"类别6(连续提钻具)召回率: {class6_recall:.4f}")

            self.log(f"最终模型性能: 准确率={test_acc:.4f}, F1={test_f1:.4f}")

            return self.model

        except Exception as e:
            self.log(f"训练模型时出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            return None

    def evaluate_model(self, X_test, y_test):
        """评估模型性能"""
        self.log("评估模型性能")

        if self.model is None:
            self.log("错误: 模型未训练成功，无法评估")
            return None

        try:
            # 预测
            y_pred = np.argmax(self.model.predict(X_test), axis=1)

            # 计算整体指标
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='macro', zero_division=0)
            recall = recall_score(y_test, y_pred, average='macro', zero_division=0)
            f1 = f1_score(y_test, y_pred, average='macro', zero_division=0)

            self.log(f"测试集性能指标:")
            self.log(f"  准确率 (Accuracy): {accuracy:.4f}")
            self.log(f"  精确率 (Precision): {precision:.4f}")
            self.log(f"  召回率 (Recall): {recall:.4f}")
            self.log(f"  F1分数 (F1-Score): {f1:.4f}")

            # 分类报告
            report = classification_report(y_test, y_pred, digits=4, zero_division=0)
            self.log("\n分类报告:\n" + report)

            # 混淆矩阵
            cm = confusion_matrix(y_test, y_pred)
            self.confusion_matrix = cm

            # 原始标签评估
            y_test_original = np.array([self.encoded_to_original_map.get(label, label) for label in y_test])
            y_pred_original = np.array([self.encoded_to_original_map.get(label, label) for label in y_pred])

            accuracy_original = accuracy_score(y_test_original, y_pred_original)
            f1_original = f1_score(y_test_original, y_pred_original, average='macro', zero_division=0)

            self.log(f"\n基于原始标签的性能指标:")
            self.log(f"  准确率: {accuracy_original:.4f}")
            self.log(f"  F1分数: {f1_original:.4f}")

            return {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'confusion_matrix': cm,
                'classification_report': report,
                'y_pred': y_pred,
                'accuracy_original': accuracy_original,
                'f1_original': f1_original
            }

        except Exception as e:
            self.log(f"评估模型时出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            return None

    def plot_training_history(self):
        """绘制训练历史"""
        if not self.training_history:
            self.log("警告: 没有训练历史记录可绘制")
            return

        try:
            # 提取数据
            iterations = [r['iteration'] + 1 for r in self.training_history]
            train_acc = [r['train_acc'] for r in self.training_history]
            test_acc = [r['test_acc'] for r in self.training_history]
            test_f1 = [r['test_f1'] for r in self.training_history]

            # 创建图表
            fig, axes = plt.subplots(2, 1, figsize=(12, 10))

            # 1. 准确率曲线
            axes[0].plot(iterations, train_acc, 'b-', label='训练集准确率', linewidth=2)
            axes[0].plot(iterations, test_acc, 'r-', label='测试集准确率', linewidth=2)
            axes[0].set_xlabel('迭代次数')
            axes[0].set_ylabel('准确率')
            axes[0].set_title('训练过程 - 准确率')
            axes[0].legend()
            axes[0].grid(True, alpha=0.3)

            # 2. F1分数曲线
            axes[1].plot(iterations, test_f1, 'g-', label='测试集F1分数', linewidth=2)
            axes[1].set_xlabel('迭代次数')
            axes[1].set_ylabel('F1分数')
            axes[1].set_title('训练过程 - F1分数')
            axes[1].legend()
            axes[1].grid(True, alpha=0.3)

            plt.tight_layout()

            output_path = os.path.join(self.output_dir, 'training_history.png')
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()

        except Exception as e:
            self.log(f"绘制训练历史时出错: {str(e)}")

    def plot_confusion_matrix(self):
        """绘制混淆矩阵"""
        if not hasattr(self, 'confusion_matrix'):
            self.log("错误: 请先调用evaluate_model方法")
            return

        try:
            # 创建类别标签
            n_classes = len(self.confusion_matrix)
            class_labels = []
            for i in range(n_classes):
                original_class = self.encoded_to_original_map[i]
                class_labels.append(f"{original_class}-{self.workstate_mapping.get(original_class, '未知')}")

            # 绘制混淆矩阵
            plt.figure(figsize=(12, 10))
            plt.title('混淆矩阵')
            sns.heatmap(self.confusion_matrix, annot=True, fmt='d', cmap='Blues',
                        xticklabels=class_labels, yticklabels=class_labels)
            plt.xlabel('预测标签')
            plt.ylabel('真实标签')
            plt.xticks(rotation=45)
            plt.yticks(rotation=45)
            plt.tight_layout()

            output_path = os.path.join(self.output_dir, 'confusion_matrix.png')
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()

        except Exception as e:
            self.log(f"绘制混淆矩阵时出错: {str(e)}")

    def plot_feature_importance(self, top_n=20):
        """绘制特征重要性"""
        if not self.model:
            self.log("错误: 模型未训练")
            return

        try:
            # 获取特征重要性
            importance = self.model.feature_importance(importance_type='gain')

            feature_importance = pd.DataFrame({
                'feature': self.feature_columns,
                'importance': importance
            }).sort_values(by='importance', ascending=False)

            # 选择top N
            top_features = feature_importance.head(top_n)

            # 绘制图表
            plt.figure(figsize=(10, 12))
            ax = sns.barplot(x='importance', y='feature', data=top_features)

            for i, v in enumerate(top_features['importance']):
                ax.text(v + 0.001, i, f"{v:.1f}", va='center')

            plt.title(f'特征重要性 (Top {len(top_features)})')
            plt.xlabel('重要性')
            plt.ylabel('特征')
            plt.tight_layout()

            output_path = os.path.join(self.output_dir, 'feature_importance.png')
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()

            # 打印重要特征
            self.log("\n重要特征排名:")
            for i, (feature, importance) in enumerate(
                    zip(top_features['feature'][:10], top_features['importance'][:10])):
                self.log(f"  {i + 1}. {feature}: {importance:.1f}")

            return feature_importance

        except Exception as e:
            self.log(f"绘制特征重要性时出错: {str(e)}")
            return None

    def save_model(self, model_path=None):
        """保存模型"""
        if model_path is None:
            model_path = os.path.join(self.output_dir, "lightgbm_model.txt")

        if not self.model:
            self.log("错误: 模型未训练")
            return False

        try:
            # 保存LightGBM模型
            self.model.save_model(model_path)

            # 保存标签映射
            mapping_path = os.path.join(self.output_dir, "label_mapping.json")
            import json

            mapping_data = {
                'original_to_encoded': {str(k): int(v) for k, v in self.original_to_encoded_map.items()},
                'encoded_to_original': {str(k): int(v) for k, v in self.encoded_to_original_map.items()},
                'workstate_mapping': {str(k): str(v) for k, v in self.workstate_mapping.items()},
                'features': self.feature_columns
            }

            with open(mapping_path, 'w', encoding='utf-8') as f:
                json.dump(mapping_data, f, ensure_ascii=False, indent=4)

            # 保存特征缩放器
            scaler_path = os.path.join(self.output_dir, "scaler.joblib")
            joblib.dump(self.scaler, scaler_path)

            self.log(f"模型保存成功: {model_path}")
            self.log(f"标签映射保存成功: {mapping_path}")
            self.log(f"特征缩放器保存成功: {scaler_path}")
            return True
        except Exception as e:
            self.log(f"错误: 模型保存失败: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            return False

    def save_training_log(self, log_path=None):
        """保存训练日志"""
        if log_path is None:
            log_path = os.path.join(self.output_dir, "training_log.txt")

        try:
            with open(log_path, "w", encoding="utf-8") as f:
                for entry in self.log_entries:
                    f.write(entry + "\n")
            self.log(f"训练日志保存成功: {log_path}")
            return True
        except Exception as e:
            self.log(f"错误: 训练日志保存失败: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            return False

    def run_pipeline(self, data_path="optimized_data.csv", test_size=0.2):
        """执行完整训练流程"""
        self.log("开始钻井工况识别模型优化训练流程")

        try:
            # 加载数据
            df = self.load_data(data_path)
            if df is None:
                return False

            # 准备数据
            X_train, X_test, y_train, y_test = self.prepare_data(df, test_size=test_size, random_state=42)

            # 训练模型
            self.train_model(X_train, y_train, X_test, y_test)

            # 评估模型
            eval_results = self.evaluate_model(X_test, y_test)

            if not eval_results:
                self.log("模型评估失败")
                return False

            # 生成图表
            self.plot_training_history()
            self.plot_confusion_matrix()
            self.plot_feature_importance()

            # 保存模型和日志
            self.save_model()
            self.save_training_log()

            self.log("训练流程完成!")
            return eval_results

        except Exception as e:
            self.log(f"执行训练流程时出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            return False


if __name__ == "__main__":
    # 创建钻井工况识别器
    classifier = DrillingConditionIdentifier(output_dir="drilling_results", verbose=True)

    try:
        # 执行训练流程
        results = classifier.run_pipeline(
            data_path="optimized_data.csv",
            test_size=0.2
        )

        if results:
            print("\n" + "=" * 60)
            print("钻井工况识别模型训练完成!")
            print(f"最终准确率: {results['accuracy']:.4f}")
            print(f"最终F1分数: {results['f1']:.4f}")
            print(f"原始标签准确率: {results['accuracy_original']:.4f}")
            print(f"原始标签F1分数: {results['f1_original']:.4f}")
            print(f"精确率: {results['precision']:.4f}")
            print(f"召回率: {results['recall']:.4f}")
            print(f"\n所有输出文件已保存到: {classifier.output_dir}")
            print("=" * 60)

    except Exception as e:
        print(f"\n错误: {str(e)}")
        import traceback

        traceback.print_exc()