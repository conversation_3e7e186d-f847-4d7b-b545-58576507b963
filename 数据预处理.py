# 北京大学智能学院
# ZEROLab实验室
# 学号：2303562246
# 学生：孜克如拉·艾尼瓦尔


import pandas as pd
import numpy as np
from datetime import datetime
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import VarianceThreshold
import os


class OptimizedDrillingPreprocessor:
    """优化的钻井工况数据预处理系统"""

    def __init__(self):
        self.numeric_columns = [
            "井深钻头差", "井深", "钻头位置", "大钩高度", "大钩负荷",
            "泵冲次1", "泵冲次2", "泵冲次3", "总泵冲", "入口流量",
            "出口流量(百分)", "钻压", "转盘转速", "扭矩",
            "立管压力", "入口密度", "出口密度"
        ]
        self.processing_log = []
        self.statistics = {}
        self.scaler = StandardScaler()

    def log(self, message):
        """记录处理步骤和关键统计信息"""
        log_entry = f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {message}"
        self.processing_log.append(log_entry)
        print(log_entry)

    def load_data(self, file_path="锋探1.csv"):
        """优化的数据加载步骤"""
        self.log("步骤1: 优化数据加载")

        # 智能检测分隔符
        try:
            # 先尝试逗号分隔符
            df = pd.read_csv(
                file_path,
                delimiter=",",
                encoding="gbk"
            )
            self.log(f"使用逗号分隔符加载数据: {df.shape[0]}行 × {df.shape[1]}列")
        except:
            # 如果失败，尝试制表符
            try:
                df = pd.read_csv(
                    file_path,
                    delimiter="\t",
                    encoding="gbk"
                )
                self.log(f"使用制表符加载数据: {df.shape[0]}行 × {df.shape[1]}列")
            except:
                # 最后尝试空格分隔符
                df = pd.read_csv(
                    file_path,
                    sep=r"\s+",
                    engine="python",
                    encoding="gbk"
                )
                self.log(f"使用空格分隔符加载数据: {df.shape[0]}行 × {df.shape[1]}列")

        # 记录原始数据量
        self.statistics['original_rows'] = df.shape[0]
        self.statistics['original_columns'] = df.shape[1]

        # 确保列名正确
        if len(df.columns) == 1 and ',' in df.columns[0]:
            # 处理CSV格式错误的情况
            self.log("检测到CSV格式错误，修复中...")
            first_col_name = df.columns[0]
            all_cols = [col.strip() for col in first_col_name.split(',')]

            # 将第一行拆分为列
            first_row = df.iloc[0, 0].split(',')
            df = pd.DataFrame([first_row], columns=all_cols)
            for i in range(1, len(df)):
                row_values = df.iloc[i, 0].split(',')
                if len(row_values) == len(all_cols):
                    df.loc[i] = row_values

            # 删除原始列
            df = df.drop(columns=[first_col_name])
            self.log(f"CSV格式修复完成: {df.shape[0]}行 × {df.shape[1]}列")

        self.log(f"列名: {list(df.columns)[:10]}...等{len(df.columns)}列")
        return df

    def parse_datetime(self, df):
        """优化的时间列解析"""
        self.log("步骤2: 优化时间列解析")

        # 确保列存在
        if "创建时间" in df.columns:
            df["创建时间"] = pd.to_datetime(df["创建时间"], errors="coerce")
            self.log("创建时间列解析完成")
        else:
            self.log("警告: 未找到创建时间列")

        if "远程服务器时间" in df.columns:
            df["远程服务器时间"] = pd.to_datetime(df["远程服务器时间"], errors="coerce")

            # 计算时间范围和分布
            time_range = df["远程服务器时间"].max() - df["远程服务器时间"].min()
            time_span_days = time_range.total_seconds() / (24 * 60 * 60)
            self.log(f"远程服务器时间范围: {df['远程服务器时间'].min()} 至 {df['远程服务器时间'].max()}")
            self.log(f"数据时间跨度: {time_span_days:.1f}天")

            # 记录时间统计
            self.statistics['time_span_days'] = time_span_days
        else:
            self.log("警告: 未找到远程服务器时间列")

        return df

    def convert_numeric(self, df):
        """优化的数值类型转换"""
        self.log("步骤3: 优化数值类型转换")

        converted_count = 0
        conversion_stats = {}

        for col in self.numeric_columns:
            if col in df.columns:
                # 记录转换前的非缺失值数量
                non_null_before = df[col].count()

                # 转换为数值类型
                df[col] = pd.to_numeric(df[col], errors="coerce")

                # 记录转换后的非缺失值数量
                non_null_after = df[col].count()
                conversion_rate = non_null_after / non_null_before if non_null_before > 0 else 0

                conversion_stats[col] = {
                    'before': non_null_before,
                    'after': non_null_after,
                    'rate': conversion_rate
                }

                converted_count += 1
                self.log(f"转换 {col} 为数值类型 (成功率: {conversion_rate:.1%})")

        self.log(f"共转换 {converted_count} 个数值列")
        self.statistics['conversion_stats'] = conversion_stats

        return df

    def analyze_missing_values(self, df):
        """分析缺失值模式"""
        self.log("步骤4.1: 缺失值模式分析")

        # 获取实际存在的数值列
        actual_numeric_cols = [col for col in self.numeric_columns if col in df.columns]

        # 计算缺失值矩阵
        missing_matrix = df[actual_numeric_cols].isna()

        # 分析缺失值模式
        missing_patterns = missing_matrix.sum(axis=1).value_counts().sort_index()

        # 每列缺失值统计
        missing_stats = {}
        for col in actual_numeric_cols:
            missing_count = df[col].isna().sum()
            missing_rate = missing_count / len(df)
            missing_stats[col] = {
                'count': missing_count,
                'rate': missing_rate
            }
            self.log(f"{col} 缺失率: {missing_rate:.2%}")

        # 记录缺失值统计
        self.statistics['missing_stats'] = missing_stats
        self.statistics['missing_patterns'] = missing_patterns.to_dict()

        # 返回缺失值分析结果
        return missing_stats

    def handle_missing_values(self, df):
        """优化的缺失值处理"""
        self.log("步骤4.2: 优化缺失值处理")

        # 首先进行缺失值分析
        missing_stats = self.analyze_missing_values(df)

        # 判断缺失率超过30%的列
        high_missing_cols = [col for col, stats in missing_stats.items()
                             if stats['rate'] > 0.30]

        if high_missing_cols:
            self.log(f"删除缺失率超过30%的列: {high_missing_cols}")
            df = df.drop(columns=high_missing_cols)

        # 更新数值列列表
        remain_cols = [col for col in self.numeric_columns if col in df.columns]

        # 智能缺失值处理: 按时间序列线性插值
        if 'remote_server_time' in df.columns:
            self.log("按时间序列进行线性插值...")
            df.set_index('remote_server_time', inplace=True)
            df[remain_cols] = df[remain_cols].interpolate(method='time')
            df.reset_index(inplace=True)
        else:
            # 常规线性插值
            self.log("执行标准线性插值...")
            df[remain_cols] = df[remain_cols].interpolate(method="linear", limit_direction='both')

        # 均值填补剩余缺失值
        self.log("执行均值填补...")
        for col in remain_cols:
            if df[col].isna().any():
                col_mean = df[col].mean()
                missing_after_interp = df[col].isna().sum()
                df[col] = df[col].fillna(col_mean)
                self.log(f"  {col}: 填补{missing_after_interp}个缺失值，均值={col_mean:.2f}")

        # 检查处理后的缺失值
        total_missing_after = df[remain_cols].isna().sum().sum()
        self.log(f"处理后剩余缺失值: {total_missing_after}")
        self.statistics['missing_after_handling'] = total_missing_after

        return df

    def analyze_outliers(self, df):
        """分析异常值分布"""
        self.log("步骤5.1: 异常值分布分析")

        numeric_cols = [col for col in self.numeric_columns if col in df.columns]
        outlier_stats = {}

        for col in numeric_cols:
            # 计算四分位数
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1

            # 定义异常值边界
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR

            # 计算异常值数量和比例
            lower_outliers = (df[col] < lower_bound).sum()
            upper_outliers = (df[col] > upper_bound).sum()
            total_outliers = lower_outliers + upper_outliers
            outlier_rate = total_outliers / len(df)

            outlier_stats[col] = {
                'lower_bound': lower_bound,
                'upper_bound': upper_bound,
                'lower_outliers': lower_outliers,
                'upper_outliers': upper_outliers,
                'total_outliers': total_outliers,
                'outlier_rate': outlier_rate
            }

        # 异常值比例过高的特征
        high_outlier_cols = [col for col, stats in outlier_stats.items()
                             if stats['outlier_rate'] > 0.1]
        if high_outlier_cols:
            self.log(f"异常值比例过高的特征: {high_outlier_cols}")

        self.statistics['outlier_stats'] = outlier_stats
        return outlier_stats

    def handle_outliers(self, df):
        """优化的异常值处理"""
        self.log("步骤5.2: 优化异常值处理")

        # 首先分析异常值
        outlier_stats = self.analyze_outliers(df)

        numeric_cols = [col for col in self.numeric_columns if col in df.columns]

        for col in numeric_cols:
            if col in outlier_stats:
                stats = outlier_stats[col]

                # 原始数据分布统计
                original_mean = df[col].mean()
                original_std = df[col].std()

                # 应用限幅
                df[col] = df[col].clip(
                    lower=stats['lower_bound'],
                    upper=stats['upper_bound']
                )

                # 处理后的统计
                clipped_mean = df[col].mean()
                clipped_std = df[col].std()

                # 记录变化
                mean_change = (clipped_mean - original_mean) / original_mean if original_mean != 0 else 0
                std_change = (clipped_std - original_std) / original_std if original_std != 0 else 0

                self.log(f"{col}: 处理 {stats['total_outliers']} 个异常值 " +
                         f"(均值变化: {mean_change:.2%}, 标准差变化: {std_change:.2%})")

        return df

    def optimize_sorting_deduplication(self, df):
        """优化的排序与去重处理"""
        self.log("步骤6: 优化排序与去重")

        original_count = len(df)

        # 优化1: 确保时间列存在并且格式正确
        if "远程服务器时间" in df.columns:
            # 排序
            df = df.sort_values("远程服务器时间")

            # 优化2: 自适应去重策略
            if "井深" in df.columns:
                # 分析数据变化频率
                time_diff = df["远程服务器时间"].diff()
                depth_diff = df["井深"].diff().abs()

                # 计算最小有意义变化阈值 (使用0.1百分位数而不是0)
                min_meaningful_change = max(depth_diff.quantile(0.001), 0.001)
                self.log(f"井深最小有意义变化阈值: {min_meaningful_change:.6f}")

                # 智能去重: 保留时间变化或井深有显著变化的记录
                duplicate_mask = (time_diff <= pd.Timedelta(seconds=30)) & (depth_diff < min_meaningful_change)
                duplicate_mask = duplicate_mask.fillna(False)

                # 创建一个唯一ID列用于更智能的去重
                df['dedup_group'] = (~duplicate_mask).cumsum()

                # 在每个组内保留代表性样本(第一个)
                df = df.drop_duplicates(subset=['dedup_group'], keep='first')
                df = df.drop(columns=['dedup_group'])
            else:
                # 如果没有井深列，使用更保守的去重策略
                df = df.drop_duplicates(subset=["远程服务器时间"], keep="first")

            # 记录去重后的数据量
            removed_count = original_count - len(df)
            removal_rate = removed_count / original_count

            self.log(f"移除 {removed_count} 条重复数据 (去重率: {removal_rate:.1%})")
            self.statistics['deduplication_rate'] = removal_rate
        else:
            self.log("警告: 未找到远程服务器时间列，跳过排序去重")

        return df

    def optimize_moving_average(self, df):
        """优化的滑动平均处理"""
        self.log("步骤7: 优化滑动平均")

        numeric_cols = [col for col in self.numeric_columns if col in df.columns]

        # 自适应窗口大小确定
        if len(df) >= 100:
            # 基于数据量和变化率动态确定窗口大小
            if 'remote_server_time' in df.columns:
                # 基于时间间隔调整窗口大小
                median_interval = df['remote_server_time'].diff().median().total_seconds()
                # 目标是约5-10分钟的时间窗口
                target_window_seconds = 300  # 5分钟
                optimal_window = max(4, min(20, int(target_window_seconds / median_interval)))
            else:
                # 基于数据特性确定窗口大小
                # 较小窗口(4-6)用于高频变化数据，较大窗口(12-20)用于低频变化
                if any(col in df.columns for col in ['井深', '钻头位置']):
                    # 井深和钻头位置通常变化较慢
                    optimal_window = 12
                else:
                    # 默认中等窗口大小
                    optimal_window = 8
        else:
            # 数据量少时使用较小窗口
            optimal_window = max(2, min(len(df) // 10, 8))

        self.log(f"优化后的滑动窗口大小: {optimal_window}")
        self.statistics['moving_average_window'] = optimal_window

        # 应用滑动平均
        smoothing_effects = {}
        for col in numeric_cols:
            # 记录原始标准差
            original_std = df[col].std()

            # 应用滑动平均
            ma_col = f"{col}_ma{optimal_window}"
            df[ma_col] = df[col].rolling(window=optimal_window, min_periods=1, center=True).mean()

            # 计算平滑效果
            smoothed_std = df[ma_col].std()
            smoothing_effect = (original_std - smoothed_std) / original_std if original_std != 0 else 0
            smoothing_effects[col] = smoothing_effect

            self.log(f"应用 {col} {optimal_window}点滑动平均 (平滑效果: {smoothing_effect:.1%})")

        # 记录平均平滑效果
        avg_smoothing = np.mean([effect for effect in smoothing_effects.values() if not np.isnan(effect)])
        self.log(f"平均平滑效果: {avg_smoothing:.1%}")
        self.statistics['avg_smoothing_effect'] = avg_smoothing

        return df

    def optimize_feature_engineering(self, df):
        """优化的特征工程"""
        self.log("步骤8: 优化特征工程")

        # 优化1: 基础差分特征（确保稳定性）
        if "井深" in df.columns:
            df["Δ井深"] = df["井深"].diff().fillna(0)
            # 使用指数移动平均平滑差分曲线
            df["Δ井深_smooth"] = df["Δ井深"].ewm(span=5).mean()
            self.log("创建平滑的井深变化特征")

        if "钻头位置" in df.columns:
            df["Δ钻头位置"] = df["钻头位置"].diff().fillna(0)
            df["Δ钻头位置_smooth"] = df["Δ钻头位置"].ewm(span=5).mean()
            self.log("创建平滑的钻头位置变化特征")

        # 优化2: 鲁棒的组合特征
        if "泵冲次1" in df.columns and "泵冲次2" in df.columns:
            # 更稳定的泵冲比计算
            denominator = df["泵冲次2"].replace(0, np.nan)
            df["泵冲比"] = (df["泵冲次1"] / denominator).fillna(0).replace([np.inf, -np.inf], 0)
            # 限制异常值
            df["泵冲比"] = df["泵冲比"].clip(lower=0, upper=df["泵冲比"].quantile(0.99))
            self.log("创建稳定的泵冲比特征")

        if "钻压" in df.columns and "转盘转速" in df.columns:
            df["机械钻速"] = df["钻压"] * df["转盘转速"]
            # 归一化处理
            if df["机械钻速"].std() > 0:
                df["机械钻速_norm"] = (df["机械钻速"] - df["机械钻速"].mean()) / df["机械钻速"].std()
            self.log("创建归一化机械钻速特征")

        # 优化3: 高级时间特征
        if "远程服务器时间" in df.columns:
            # 时间周期特征
            df["小时"] = df["远程服务器时间"].dt.hour
            df["分钟"] = df["远程服务器时间"].dt.minute
            df["min_of_day"] = df["小时"] * 60 + df["分钟"]

            # 昼夜周期特征 (使用正弦变换保持连续性)
            df["hour_sin"] = np.sin(2 * np.pi * df["小时"] / 24)
            df["hour_cos"] = np.cos(2 * np.pi * df["小时"] / 24)

            # 移除中间列
            df = df.drop(columns=["小时", "分钟"])
            self.log("创建周期性时间特征")

        # 优化4: 相对压力特征
        if all(col in df.columns for col in ["立管压力", "入口压力"]):
            df["压力比"] = df["立管压力"] / df["入口压力"].replace(0, np.nan)
            df["压力比"] = df["压力比"].fillna(df["压力比"].median()).clip(
                lower=df["压力比"].quantile(0.01),
                upper=df["压力比"].quantile(0.99)
            )
            self.log("创建压力比特征")

        # 优化5: 一阶导数特征
        for col in ["井深", "钻头位置", "大钩高度", "钻压"]:
            if col in df.columns:
                df[f"{col}_速率"] = df[col].diff().fillna(0).ewm(span=3).mean()
                self.log(f"创建 {col} 速率特征")

        # 优化6: 波动性特征
        for col in ["扭矩", "钻压", "转盘转速"]:
            if col in df.columns:
                # 计算局部波动率
                df[f"{col}_波动"] = df[col].rolling(window=5, min_periods=1).std() / df[col].rolling(window=5,
                                                                                                     min_periods=1).mean()
                df[f"{col}_波动"] = df[f"{col}_波动"].fillna(0).replace([np.inf, -np.inf], 0)
                self.log(f"创建 {col} 波动特征")

        # 记录新特征数量
        original_features = self.numeric_columns + ["创建时间", "远程服务器时间"]
        new_features = [col for col in df.columns if col not in original_features]
        self.log(f"共创建 {len(new_features)} 个新特征")
        self.statistics['new_feature_count'] = len(new_features)

        return df

    def optimize_feature_selection(self, df):
        """特征选择优化"""
        self.log("步骤9: 特征选择优化")

        # 排除非数值列
        numeric_df = df.select_dtypes(include=[np.number])
        original_features = numeric_df.columns.tolist()

        # 开始记录特征重要性得分
        feature_importance = {}

        # 1. 方差阈值过滤 - 移除低方差特征
        variance_selector = VarianceThreshold(threshold=0.001)
        try:
            # 填充缺失值以便计算方差
            filled_data = numeric_df.fillna(numeric_df.mean())
            variance_selector.fit(filled_data)

            # 获取通过方差筛选的特征
            selected_features_mask = variance_selector.get_support()
            selected_features = [col for col, selected in zip(original_features, selected_features_mask) if selected]

            # 计算每个特征的方差
            variances = variance_selector.variances_
            for col, var in zip(original_features, variances):
                feature_importance[col] = {'variance': var}

            low_var_features = [col for col, selected in zip(original_features, selected_features_mask) if not selected]
            self.log(f"低方差特征 ({len(low_var_features)}): {low_var_features[:5]}...")
        except Exception as e:
            self.log(f"方差筛选异常: {str(e)}")
            selected_features = original_features

        # 2. 相关性分析 - 识别高度相关特征
        correlation_matrix = numeric_df[selected_features].corr()
        high_correlation_pairs = []

        for i in range(len(selected_features)):
            for j in range(i + 1, len(selected_features)):
                if abs(correlation_matrix.iloc[i, j]) > 0.9:  # 相关系数阈值
                    col1, col2 = selected_features[i], selected_features[j]
                    corr = correlation_matrix.iloc[i, j]
                    high_correlation_pairs.append((col1, col2, corr))

                    # 更新特征重要性
                    if col1 in feature_importance:
                        feature_importance[col1]['high_correlation'] = True
                    if col2 in feature_importance:
                        feature_importance[col2]['high_correlation'] = True

        self.log(f"高相关特征对: {len(high_correlation_pairs)}对")
        if len(high_correlation_pairs) > 0:
            self.log(f"示例高相关对: {high_correlation_pairs[0]}")

        # 3. 特征优化建议
        if high_correlation_pairs:
            redundant_features = set()
            for col1, col2, _ in high_correlation_pairs:
                # 优先保留原始特征而非衍生特征
                if '_ma' in col2 or '_' in col2:
                    redundant_features.add(col2)
                elif '_ma' in col1 or '_' in col1:
                    redundant_features.add(col1)
                else:
                    # 都是原始特征，随机选择一个
                    redundant_features.add(col2)

            self.log(f"建议移除的冗余特征: {len(redundant_features)}个")
            if redundant_features:
                self.log(f"示例冗余特征: {list(redundant_features)[:5]}...")

        # 返回特征统计信息
        self.statistics['feature_importance'] = feature_importance
        self.statistics['high_correlation_pairs'] = high_correlation_pairs

        return df

    def save_results(self, df, output_dir="./"):
        """保存处理结果"""
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 保存处理后的数据
        output_file = os.path.join(output_dir, "optimized_data.csv")
        df.to_csv(output_file, index=False, encoding="utf-8")
        self.log(f"保存优化后数据到 {output_file}")

        # 保存处理日志
        log_file = os.path.join(output_dir, "preprocessing_log.txt")
        with open(log_file, "w", encoding="utf-8") as f:
            for entry in self.processing_log:
                f.write(entry + "\n")
        self.log(f"保存处理日志到 {log_file}")

        # 保存关键统计信息
        stats_file = os.path.join(output_dir, "preprocessing_stats.txt")
        with open(stats_file, "w", encoding="utf-8") as f:
            f.write("=== 钻井数据预处理统计报告 ===\n\n")

            # 数据量统计
            f.write("1. 数据量统计:\n")
            f.write(f"   原始数据: {self.statistics.get('original_rows', 'N/A')} 行\n")
            f.write(f"   处理后数据: {len(df)} 行\n")
            f.write(f"   数据保留率: {len(df) / self.statistics.get('original_rows', 1):.1%}\n\n")

            # 特征统计
            f.write("2. 特征统计:\n")
            f.write(f"   原始特征: {self.statistics.get('original_columns', 'N/A')} 列\n")
            f.write(f"   新增特征: {self.statistics.get('new_feature_count', 'N/A')} 列\n")
            f.write(f"   最终特征: {df.shape[1]} 列\n\n")

            # 数据质量统计
            f.write("3. 数据质量统计:\n")
            f.write(f"   缺失值处理后残余: {self.statistics.get('missing_after_handling', 'N/A')}\n")
            #f.write(f"   数据去重率: {self.statistics.get('deduplication_rate', 'N/A'):.1%}\n")
            f.write(f"   滑动平均窗口大小: {self.statistics.get('moving_average_window', 'N/A')}\n")
            f.write(f"   平滑效果: {self.statistics.get('avg_smoothing_effect', 'N/A'):.1%}\n\n")

            # 特征工程评估
            f.write("4. 特征工程评估:\n")
            f.write(f"   高相关特征对: {len(self.statistics.get('high_correlation_pairs', []))}\n")

            # 建议优化项
            f.write("\n5. 优化建议:\n")
            if self.statistics.get('deduplication_rate', 0) > 0.9:
                f.write("   - 数据去重率过高，建议检查去重逻辑\n")
            if len(self.statistics.get('high_correlation_pairs', [])) > 10:
                f.write("   - 特征冗余度高，可考虑进一步降维\n")

        self.log(f"保存统计报告到 {stats_file}")

        return output_file, log_file, stats_file

    def evaluate_quality(self, df):
        """评估数据质量"""
        self.log("最终数据质量评估")

        # 1. 样本数量充足性
        min_required_samples = 1000
        samples_adequacy = "充足" if len(df) >= min_required_samples else "不足"
        self.log(f"样本数量 ({len(df)}): {samples_adequacy}")

        # 2. 特征质量评估
        numeric_features = df.select_dtypes(include=[np.number]).columns
        zero_var_features = [col for col in numeric_features
                             if df[col].std() == 0 or pd.isna(df[col].std())]

        if zero_var_features:
            self.log(f"警告: 存在 {len(zero_var_features)} 个零方差特征")

        # 3. 数据分布评估
        skewed_features = []
        for col in numeric_features:
            if df[col].skew() > 3 or df[col].skew() < -3:
                skewed_features.append((col, df[col].skew()))

        if skewed_features:
            self.log(f"高偏态特征: {len(skewed_features)}个")

        # 4. 给出最终质量评级
        quality_issues = []
        if len(df) < min_required_samples:
            quality_issues.append("样本量不足")
        if len(zero_var_features) > 0:
            quality_issues.append("存在零方差特征")
        if len(skewed_features) > len(numeric_features) * 0.3:
            quality_issues.append("大量特征分布偏态")

        if not quality_issues:
            quality_rating = "优"
        elif len(quality_issues) == 1:
            quality_rating = "良"
        else:
            quality_rating = "需改进"

        # 模型预期性能评估
        expected_performance = "85-90%" if quality_rating == "需改进" else "90-95%" if quality_rating == "良" else "95-99%"

        self.log(f"数据质量评级: {quality_rating}")
        self.log(f"预期模型准确率: {expected_performance}")

        # 保存评估结果
        self.statistics['quality_rating'] = quality_rating
        self.statistics['expected_performance'] = expected_performance
        self.statistics['quality_issues'] = quality_issues

        return quality_rating, expected_performance

    def process_all(self, file_path="锋探1.csv", output_dir="./"):
        """执行完整优化预处理流程"""
        self.log("开始执行优化的数据预处理流程")

        # 执行所有预处理步骤
        df = self.load_data(file_path)
        df = self.parse_datetime(df)
        df = self.convert_numeric(df)
        df = self.handle_missing_values(df)
        df = self.handle_outliers(df)
        df = self.optimize_sorting_deduplication(df)
        df = self.optimize_moving_average(df)
        df = self.optimize_feature_engineering(df)
        df = self.optimize_feature_selection(df)

        # 数据质量评估
        quality_rating, expected_performance = self.evaluate_quality(df)

        # 保存结果
        output_file, log_file, stats_file = self.save_results(df, output_dir)

        self.log("优化数据预处理流程完成")

        return df, output_file


if __name__ == "__main__":
    # 创建处理器实例
    processor = OptimizedDrillingPreprocessor()

    try:
        # 执行预处理
        processed_data, output_file = processor.process_all()

        print("\n预处理完成！")
        print(f"处理后数据形状: {processed_data.shape}")
        print(f"保存至: {output_file}")
        print(f"预期模型准确率: {processor.statistics['expected_performance']}")

        # 显示关键特征
        important_features = []
        for col, stats in processor.statistics.get('feature_importance', {}).items():
            if 'variance' in stats and stats['variance'] > 0.01:
                important_features.append((col, stats['variance']))

        print("\n关键特征:")
        for feat, importance in sorted(important_features, key=lambda x: x[1], reverse=True)[:10]:
            print(f"- {feat}")

    except Exception as e:
        print(f"\n错误: {str(e)}")
        import traceback

        traceback.print_exc()